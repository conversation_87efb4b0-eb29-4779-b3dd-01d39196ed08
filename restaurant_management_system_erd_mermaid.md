# Restaurant Management System ERD (Mermaid)

```mermaid
erDiagram
    USERS {
        int id PK
        varchar name
        varchar email UK
        varchar password
        varchar phone
        text address
        timestamp created_at
    }

    ADMIN {
        int id PK
        varchar username UK
        varchar password
        varchar name
        varchar email
        timestamp created_at
    }

    MENU_ITEMS {
        int id PK
        varchar name
        varchar category
        decimal price
        varchar image
        text description
        timestamp created_at
    }

    ORDERS {
        int id PK
        int user_id FK
        text items
        decimal total_price
        enum payment_method
        enum status
        timestamp date
    }

    ORDER_ITEMS {
        int id PK
        int order_id FK
        int menu_item_id FK
        int quantity
        decimal price
        timestamp created_at
    }

    RESERVATIONS {
        int id PK
        int user_id FK
        date date
        time time
        int guests
        enum status
        timestamp created_at
    }

    TABLES {
        int id PK
        int capacity
        boolean is_reserved
    }

    RECEIPTS {
        int id PK
        int order_id FK
        varchar file_path
        timestamp created_at
    }

    EMAIL_LOGS {
        int id PK
        int user_id FK
        enum type
        enum status
        timestamp timestamp
    }

    PAYMENT_TRANSACTIONS {
        int id PK
        int order_id FK
        decimal amount
        enum payment_method
        enum transaction_status
        timestamp transaction_date
        varchar transaction_reference
        text notes
    }



    USERS ||--o{ ORDERS : places
    USERS ||--o{ RESERVATIONS : makes
    ORDERS ||--o{ ORDER_ITEMS : contains
    MENU_ITEMS ||--o{ ORDER_ITEMS : included_in
    ORDERS ||--|| RECEIPTS : generates
    ORDERS ||--o{ PAYMENT_TRANSACTIONS : has
    USERS ||--o{ EMAIL_LOGS : receives
```

## Entity Descriptions

### Users
Represents registered customers who can place orders and make reservations.

### Admin
Represents system administrators who manage the restaurant system.

### Menu Items
Represents food and beverage items available for ordering.

### Orders
Represents customer orders with payment and status information.

### Order Items
Represents individual items within an order (normalized from the Orders.items JSON field).

### Reservations
Represents table reservations made by customers.

### Tables
Represents physical tables in the restaurant with capacity information.

### Receipts
Represents order receipts generated as PDF files.

### Email Logs
Tracks emails sent to users regarding orders and reservations.

### Payment Transactions
Tracks payment transactions associated with orders.

## Relationship Descriptions

1. **Users to Orders**: One-to-Many
   - A user can place many orders
   - An order belongs to one user (or can be null for guest orders)

2. **Users to Reservations**: One-to-Many
   - A user can make many reservations
   - A reservation belongs to one user (or can be null for guest reservations)

3. **Orders to Order Items**: One-to-Many
   - An order can contain many order items
   - An order item belongs to one order

4. **Menu Items to Order Items**: One-to-Many
   - A menu item can be in many order items
   - An order item refers to one menu item

5. **Orders to Receipts**: One-to-One
   - An order has one receipt
   - A receipt belongs to one order

6. **Orders to Payment Transactions**: One-to-Many
   - An order can have multiple payment transactions
   - A payment transaction belongs to one order

7. **Users to Email Logs**: One-to-Many
   - A user can have many email logs
   - An email log belongs to one user

## Enum Values

1. **Orders.payment_method**: 'cash', 'credit_card', 'debit_card', 'gcash', 'paymaya'
2. **Orders.status**: 'pending', 'preparing', 'completed', 'cancelled'
3. **Reservations.status**: 'pending', 'confirmed', 'cancelled'
4. **Email_Logs.type**: 'order', 'reservation'
5. **Email_Logs.status**: 'sent', 'failed'
6. **Payment_Transactions.transaction_status**: 'pending', 'completed', 'failed'
