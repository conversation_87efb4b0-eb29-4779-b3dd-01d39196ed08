<?php

function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($page) {
    while (ob_get_level()) {
        ob_end_clean();
    }
    ob_start();
    header("Location: " . $page);
    ob_end_flush();
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

function hasAdminPrivileges() {
    if (isset($_SESSION['admin_id'])) {
        return true;
    }

    if (isset($_SESSION['user_id'])) {
        global $conn;
        $user = getUserById($_SESSION['user_id']);
        if (!$user || empty($user['email'])) {
            return false;
        }

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM admin WHERE username = ?");
        $stmt->bind_param("s", $user['email']);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return $row['count'] > 0;
    }

    return false;
}

function getUserById($user_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

function getAdminById($admin_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM admin WHERE id = ?");
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

function getMenuItems($category = null) {
    global $conn;

    if ($category) {
        $stmt = $conn->prepare("SELECT * FROM menu_items WHERE category = ? ORDER BY name");
        $stmt->bind_param("s", $category);
    } else {
        $stmt = $conn->prepare("SELECT * FROM menu_items ORDER BY category, name");
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = $row;
    }

    return $items;
}

function getMenuCategories() {
    global $conn;
    $stmt = $conn->prepare("SELECT DISTINCT category FROM menu_items ORDER BY category");
    $stmt->execute();
    $result = $stmt->get_result();

    $categories = [];
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row['category'];
    }

    return $categories;
}

function getMenuItemById($item_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM menu_items WHERE id = ?");
    $stmt->bind_param("i", $item_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

function getUserOrders($user_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY date DESC");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $orders = [];
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }

    return $orders;
}

function getUserReservations($user_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM reservations WHERE user_id = ? ORDER BY date DESC, time DESC");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $reservations = [];
    while ($row = $result->fetch_assoc()) {
        $reservations[] = $row;
    }

    return $reservations;
}

function isTableAvailable($date, $time, $guests) {
    $available_tables = getAvailableTables($date, $time, $guests);
    return !empty($available_tables);
}

function getAvailableTables($date, $time, $guests, $exclude_reservation_id = null) {
    global $conn;

    $stmt = $conn->prepare("SELECT id, capacity FROM tables WHERE capacity >= ? ORDER BY capacity ASC");
    $stmt->bind_param("i", $guests);
    $stmt->execute();
    $result = $stmt->get_result();

    $suitable_tables = [];
    while ($row = $result->fetch_assoc()) {
        $suitable_tables[] = $row;
    }

    if (empty($suitable_tables)) {
        return [];
    }

    $query = "
        SELECT r.id, r.time, r.guests, rt.table_id
        FROM reservations r
        LEFT JOIN reservation_tables rt ON r.id = rt.reservation_id
        WHERE r.date = ?
        AND r.status IN ('pending', 'confirmed')
        AND r.time = ?
    ";

    $params = [$date, $time];
    $types = "ss";

    if ($exclude_reservation_id) {
        $query .= " AND r.id != ?";
        $params[] = $exclude_reservation_id;
        $types .= "i";
    }

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $reserved_tables = [];
    while ($row = $result->fetch_assoc()) {
        if ($row['table_id']) {
            $reserved_tables[] = $row['table_id'];
        }
    }

    $available_tables = [];
    foreach ($suitable_tables as $table) {
        if (!in_array($table['id'], $reserved_tables)) {
            $available_tables[] = $table;
        }
    }

    return $available_tables;
}

function assignTablesToReservation($reservation_id, $table_ids) {
    global $conn;

    $conn->begin_transaction();

    try {
        $stmt = $conn->prepare("DELETE FROM reservation_tables WHERE reservation_id = ?");
        $stmt->bind_param("i", $reservation_id);
        $stmt->execute();

        foreach ($table_ids as $table_id) {
            $stmt = $conn->prepare("INSERT INTO reservation_tables (reservation_id, table_id) VALUES (?, ?)");
            $stmt->bind_param("ii", $reservation_id, $table_id);
            $stmt->execute();
        }

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error assigning tables to reservation: " . $e->getMessage());
        return false;
    }
}

function getTablesForReservation($reservation_id) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT t.id, t.capacity
        FROM tables t
        JOIN reservation_tables rt ON t.id = rt.table_id
        WHERE rt.reservation_id = ?
        ORDER BY t.capacity ASC
    ");
    $stmt->bind_param("i", $reservation_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $tables = [];
    while ($row = $result->fetch_assoc()) {
        $tables[] = $row;
    }

    return $tables;
}

function findOptimalTables($available_tables, $guests) {
    usort($available_tables, function($a, $b) {
        return $a['capacity'] - $b['capacity'];
    });

    foreach ($available_tables as $table) {
        if ($table['capacity'] == $guests) {
            return [$table['id']];
        }
    }

    foreach ($available_tables as $table) {
        if ($table['capacity'] > $guests) {
            return [$table['id']];
        }
    }

    $best_combination = [];
    $best_capacity = 0;

    $count = count($available_tables);
    for ($i = 0; $i < $count; $i++) {
        for ($j = $i + 1; $j < $count; $j++) {
            $combined_capacity = $available_tables[$i]['capacity'] + $available_tables[$j]['capacity'];

            if ($combined_capacity >= $guests && ($best_capacity == 0 || $combined_capacity < $best_capacity)) {
                $best_combination = [$available_tables[$i]['id'], $available_tables[$j]['id']];
                $best_capacity = $combined_capacity;
            }
        }
    }

    if (!empty($best_combination)) {
        return $best_combination;
    }

    usort($available_tables, function($a, $b) {
        return $b['capacity'] - $a['capacity'];
    });

    return [$available_tables[0]['id']];
}

function getReservedTimeSlots($date, $guests = null) {
    global $conn;

    $reserved_times = [];

    $all_time_slots = [];
    for ($hour = 9; $hour <= 21; $hour++) {
        for ($minute = 0; $minute < 60; $minute += 30) {
            if ($hour === 21 && $minute === 30) continue;

            $time = sprintf('%02d:%02d:00', $hour, $minute);
            $all_time_slots[] = $time;
        }
    }

    foreach ($all_time_slots as $time) {
        if ($guests !== null) {
            $available_tables = getAvailableTables($date, $time, $guests);

            if (empty($available_tables)) {
                $reserved_times[] = $time;
            }
        } else {
            $query = "
                SELECT COUNT(*) as count
                FROM reservations
                WHERE date = ?
                AND status IN ('pending', 'confirmed')
                AND time = ?
            ";

            $stmt = $conn->prepare($query);
            $stmt->bind_param("ss", $date, $time);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] > 0) {
                $reserved_times[] = $time;
            }
        }
    }

    return $reserved_times;
}

function formatCurrency($amount) {
    return number_format($amount, 2);
}

function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

function formatTime($time) {
    return date('g:i A', strtotime($time));
}

function sendEmail($to, $subject, $message, $user_id, $type, $html_message = '') {
    global $conn;

    $formatted_message = wordwrap($message, 70, "\r\n");
    $mail_sent = false;

    try {
        $user_name = '';
        if ($user_id) {
            $user = getUserById($user_id);
            if ($user && !empty($user['name'])) {
                $user_name = $user['name'];
            }
        }

        if (empty($user_name)) {
            $user_name = 'Valued Customer';
        }

        if (empty($html_message)) {
            include_once __DIR__ . '/email_template.php';
            $html_message = getBasicEmailTemplate($subject, nl2br($formatted_message));
        }

        include_once __DIR__ . '/phpmailer_email.php';
        $mail_sent = sendWithPHPMailer($to, $user_name, $subject, $formatted_message, $html_message);

        $status = $mail_sent ? 'sent' : 'failed';

        $stmt = $conn->prepare("INSERT INTO email_logs (user_id, type, status, recipient, subject) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("issss", $user_id, $type, $status, $to, $subject);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Email sending error: " . $e->getMessage());
        $mail_sent = false;
    }

    return $mail_sent;
}

function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

function getPaymentTransactions($filters = []) {
    global $conn;

    $query = "SELECT pt.*, o.user_id, o.status as order_status, u.name as user_name, u.email as user_email
              FROM payment_transactions pt
              JOIN orders o ON pt.order_id = o.id
              LEFT JOIN users u ON o.user_id = u.id";

    $where_clauses = [];
    $params = [];
    $types = "";

    if (!empty($filters['transaction_status'])) {
        $where_clauses[] = "pt.transaction_status = ?";
        $params[] = $filters['transaction_status'];
        $types .= "s";
    }

    if (!empty($filters['payment_method'])) {
        $where_clauses[] = "pt.payment_method = ?";
        $params[] = $filters['payment_method'];
        $types .= "s";
    }

    if (!empty($filters['date_from'])) {
        $where_clauses[] = "DATE(pt.transaction_date) >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $where_clauses[] = "DATE(pt.transaction_date) <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }

    if (!empty($filters['search'])) {
        $search = "%" . $filters['search'] . "%";
        $where_clauses[] = "(pt.transaction_reference LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
        $params[] = $search;
        $params[] = $search;
        $params[] = $search;
        $types .= "sss";
    }

    if (!empty($where_clauses)) {
        $query .= " WHERE " . implode(" AND ", $where_clauses);
    }

    $query .= " ORDER BY pt.transaction_date DESC";

    if (!empty($filters['limit'])) {
        $query .= " LIMIT ?";
        $params[] = (int)$filters['limit'];
        $types .= "i";
    }

    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $transactions = [];
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }

    return $transactions;
}

function getPaymentTransactionById($transaction_id) {
    global $conn;

    $stmt = $conn->prepare("SELECT pt.*, o.user_id, o.status as order_status, u.name as user_name, u.email as user_email
                           FROM payment_transactions pt
                           JOIN orders o ON pt.order_id = o.id
                           LEFT JOIN users u ON o.user_id = u.id
                           WHERE pt.id = ?");
    $stmt->bind_param("i", $transaction_id);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_assoc();
}

function getPaymentTransactionsByOrderId($order_id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM payment_transactions WHERE order_id = ? ORDER BY transaction_date DESC");
    $stmt->bind_param("i", $order_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $transactions = [];
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }

    return $transactions;
}

function createPaymentTransaction($transaction_data) {
    global $conn;

    $stmt = $conn->prepare("INSERT INTO payment_transactions
                           (order_id, amount, payment_method, transaction_status, transaction_reference, notes)
                           VALUES (?, ?, ?, ?, ?, ?)");

    $stmt->bind_param(
        "idssss",
        $transaction_data['order_id'],
        $transaction_data['amount'],
        $transaction_data['payment_method'],
        $transaction_data['transaction_status'],
        $transaction_data['transaction_reference'],
        $transaction_data['notes']
    );

    if ($stmt->execute()) {
        return $stmt->insert_id;
    }

    return false;
}

function updatePaymentTransaction($transaction_id, $transaction_data) {
    global $conn;

    $set_clauses = [];
    $params = [];
    $types = "";

    foreach ($transaction_data as $key => $value) {
        if (in_array($key, ['amount', 'payment_method', 'transaction_status', 'transaction_reference', 'notes'])) {
            $set_clauses[] = "$key = ?";
            $params[] = $value;
            $types .= ($key === 'amount') ? 'd' : 's';
        }
    }

    if (empty($set_clauses)) {
        return false;
    }

    $query = "UPDATE payment_transactions SET " . implode(", ", $set_clauses) . " WHERE id = ?";
    $params[] = $transaction_id;
    $types .= "i";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);

    return $stmt->execute();
}


?>
