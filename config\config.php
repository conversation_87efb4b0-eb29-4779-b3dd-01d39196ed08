<?php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'raymart_diner');

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn->query($sql) === FALSE) {
    die("Error creating database: " . $conn->error);
}

$conn->select_db(DB_NAME);

$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating users table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS admin (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating admin table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS menu_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    image VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating menu_items table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS orders (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    items TEXT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash',
    status ENUM('pending', 'preparing', 'completed', 'cancelled') DEFAULT 'pending',
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating orders table: " . $conn->error);
}

$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'payment_method'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE orders ADD COLUMN payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash' AFTER total_price";
    $conn->query($sql);
}

$sql = "CREATE TABLE IF NOT EXISTS reservations (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    date DATE NOT NULL,
    time TIME NOT NULL,
    guests INT(11) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating reservations table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    capacity INT(11) NOT NULL,
    is_reserved BOOLEAN DEFAULT FALSE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating tables table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS receipts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating receipts table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS email_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    type ENUM('order', 'reservation', 'admin_notification') NOT NULL,
    status ENUM('sent', 'failed') NOT NULL,
    recipient VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating email_logs table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') NOT NULL,
    transaction_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    transaction_reference VARCHAR(100),
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating payment_transactions table: " . $conn->error);
}



$sql = "CREATE TABLE IF NOT EXISTS order_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    menu_item_id INT(11) NOT NULL,
    quantity INT(11) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE RESTRICT
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating order_items table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS reservation_tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT(11) NOT NULL,
    table_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE CASCADE,
    UNIQUE KEY unique_reservation_table (reservation_id, table_id)
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating reservation_tables table: " . $conn->error);
}

$result = $conn->query("SHOW COLUMNS FROM reservations LIKE 'duration'");
if ($result->num_rows > 0) {
    $sql = "ALTER TABLE reservations DROP COLUMN duration";
    $conn->query($sql);
}

$result = $conn->query("SHOW COLUMNS FROM email_logs LIKE 'recipient'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE email_logs ADD COLUMN recipient VARCHAR(100) NOT NULL AFTER status";
    $conn->query($sql);
}

$result = $conn->query("SHOW COLUMNS FROM email_logs LIKE 'subject'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE email_logs ADD COLUMN subject VARCHAR(255) NOT NULL AFTER recipient";
    $conn->query($sql);
}

$sql = "ALTER TABLE email_logs MODIFY COLUMN type ENUM('order', 'reservation', 'admin_notification') NOT NULL";
$conn->query($sql);

$admin_username = 'admin';
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$admin_name = 'Administrator';
$admin_email = '<EMAIL>';

$stmt = $conn->prepare("SELECT id FROM admin WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $stmt = $conn->prepare("INSERT INTO admin (username, password, name, email) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $admin_username, $admin_password, $admin_name, $admin_email);
    $stmt->execute();
}

$stmt = $conn->prepare("SELECT id FROM tables LIMIT 1");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    for ($i = 1; $i <= 10; $i++) {
        $capacity = ($i <= 5) ? 2 : (($i <= 8) ? 4 : 6);
        $stmt = $conn->prepare("INSERT INTO tables (capacity, is_reserved) VALUES (?, FALSE)");
        $stmt->bind_param("i", $capacity);
        $stmt->execute();
    }
}

define('SITE_NAME', 'RAYMART\'S DINER');
define('SITE_URL', 'http://localhost/restaurant-management-system3');
define('ADMIN_EMAIL', '<EMAIL>');
?>
