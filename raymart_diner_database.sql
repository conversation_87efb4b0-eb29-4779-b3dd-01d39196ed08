-- =====================================================
-- RAYMART'S DINER - Restaurant Management System
-- Database Schema SQL File
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS `raymart_diner` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `raymart_diner`;

-- =====================================================
-- TABLE: users
-- Description: Customer accounts and user information
-- =====================================================
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `email` VARCHAR(100) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `phone` VARCHAR(20) DEFAULT NULL,
    `address` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: admin
-- Description: Administrator accounts
-- =====================================================
CREATE TABLE IF NOT EXISTS `admin` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `name` VARCHAR(100) DEFAULT NULL,
    `email` VARCHAR(100) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: menu_items
-- Description: Restaurant menu items
-- =====================================================
CREATE TABLE IF NOT EXISTS `menu_items` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `category` VARCHAR(50) NOT NULL,
    `price` DECIMAL(10,2) NOT NULL,
    `image` VARCHAR(255) DEFAULT NULL,
    `description` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: orders
-- Description: Customer orders
-- =====================================================
CREATE TABLE IF NOT EXISTS `orders` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) DEFAULT NULL,
    `items` TEXT NOT NULL,
    `total_price` DECIMAL(10,2) NOT NULL,
    `payment_method` ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash',
    `status` ENUM('pending', 'preparing', 'completed', 'cancelled') DEFAULT 'pending',
    `date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: reservations
-- Description: Table reservations
-- =====================================================
CREATE TABLE IF NOT EXISTS `reservations` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) DEFAULT NULL,
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    `guests` INT(11) NOT NULL,
    `status` ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `reservations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: tables
-- Description: Restaurant tables
-- =====================================================
CREATE TABLE IF NOT EXISTS `tables` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `capacity` INT(11) NOT NULL,
    `is_reserved` BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: receipts
-- Description: Order receipts
-- =====================================================
CREATE TABLE IF NOT EXISTS `receipts` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `order_id` INT(11) NOT NULL,
    `file_path` VARCHAR(255) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `order_id` (`order_id`),
    CONSTRAINT `receipts_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: email_logs
-- Description: Email tracking and logs
-- =====================================================
CREATE TABLE IF NOT EXISTS `email_logs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) DEFAULT NULL,
    `type` ENUM('order', 'reservation', 'admin_notification') NOT NULL,
    `status` ENUM('sent', 'failed') NOT NULL,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `email_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE: payment_transactions
-- Description: Payment transaction records
-- =====================================================
CREATE TABLE IF NOT EXISTS `payment_transactions` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `order_id` INT(11) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `payment_method` ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') NOT NULL,
    `transaction_status` ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    `transaction_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `transaction_reference` VARCHAR(100) DEFAULT NULL,
    `notes` TEXT DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `order_id` (`order_id`),
    CONSTRAINT `payment_transactions_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- =====================================================
-- TABLE: reservation_tables
-- Description: Many-to-many relationship between reservations and tables
-- =====================================================
CREATE TABLE IF NOT EXISTS `reservation_tables` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `reservation_id` INT(11) NOT NULL,
    `table_id` INT(11) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_reservation_table` (`reservation_id`, `table_id`),
    KEY `reservation_id` (`reservation_id`),
    KEY `table_id` (`table_id`),
    CONSTRAINT `reservation_tables_ibfk_1` FOREIGN KEY (`reservation_id`) REFERENCES `reservations` (`id`) ON DELETE CASCADE,
    CONSTRAINT `reservation_tables_ibfk_2` FOREIGN KEY (`table_id`) REFERENCES `tables` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DEFAULT DATA INSERTION
-- =====================================================

-- Insert default admin account
-- Username: admin, Password: admin123
INSERT INTO `admin` (`username`, `password`, `name`, `email`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>')
ON DUPLICATE KEY UPDATE `username` = `username`;

-- Insert default tables (10 tables with varying capacities)
INSERT INTO `tables` (`capacity`, `is_reserved`) VALUES
(2, FALSE),  -- Table 1: 2 seats
(2, FALSE),  -- Table 2: 2 seats
(2, FALSE),  -- Table 3: 2 seats
(2, FALSE),  -- Table 4: 2 seats
(2, FALSE),  -- Table 5: 2 seats
(4, FALSE),  -- Table 6: 4 seats
(4, FALSE),  -- Table 7: 4 seats
(4, FALSE),  -- Table 8: 4 seats
(6, FALSE),  -- Table 9: 6 seats
(6, FALSE)   -- Table 10: 6 seats
ON DUPLICATE KEY UPDATE `capacity` = VALUES(`capacity`);

-- =====================================================
-- SAMPLE MENU ITEMS
-- Note: These are sample items. Adjust as needed.
-- =====================================================

-- Filipino Main Dishes
INSERT INTO `menu_items` (`name`, `category`, `price`, `description`) VALUES
('Adobong Manok', 'Main Course', 180.00, 'Classic Filipino chicken adobo cooked in soy sauce and vinegar'),
('Sinigang na Baboy', 'Main Course', 220.00, 'Pork sinigang in tamarind broth with vegetables'),
('Lechon Kawali', 'Main Course', 250.00, 'Crispy pork belly served with liver sauce'),
('Bicol Express', 'Main Course', 200.00, 'Spicy pork dish cooked in coconut milk and chili'),
('Crispy Pata', 'Main Course', 350.00, 'Deep-fried pork knuckles served with soy-vinegar dip'),
('Chicken Inasal', 'Main Course', 190.00, 'Grilled chicken marinated in lemongrass and spices'),
('Pancit Malabon', 'Main Course', 160.00, 'Thick rice noodles with seafood and vegetables'),
('Lumpiang Shanghai', 'Appetizer', 120.00, 'Filipino spring rolls filled with ground pork'),
('Sizzling Pork Sisig', 'Appetizer', 180.00, 'Sizzling chopped pork with onions and peppers');

-- Burgers
INSERT INTO `menu_items` (`name`, `category`, `price`, `description`) VALUES
('Cheeseburger', 'Burgers', 150.00, 'Juicy beef patty with cheese, lettuce, and tomato'),
('Chicken Burger', 'Burgers', 140.00, 'Grilled chicken breast with mayo and vegetables'),
('Veggie Burger', 'Burgers', 130.00, 'Plant-based patty with fresh vegetables');

-- Beverages
INSERT INTO `menu_items` (`name`, `category`, `price`, `description`) VALUES
('Apple Juice', 'Beverages', 60.00, 'Fresh apple juice'),
('Orange Juice', 'Beverages', 60.00, 'Freshly squeezed orange juice'),
('Lemonade', 'Beverages', 50.00, 'Fresh lemonade with mint'),
('Iced Tea', 'Beverages', 45.00, 'Refreshing iced tea');

-- Desserts
INSERT INTO `menu_items` (`name`, `category`, `price`, `description`) VALUES
('Leche Flan', 'Desserts', 80.00, 'Classic Filipino custard dessert'),
('Halo-Halo', 'Desserts', 120.00, 'Mixed shaved ice dessert with various toppings'),
('Buko Pandan', 'Desserts', 90.00, 'Young coconut and pandan flavored dessert'),
('Ube Ice Cream', 'Desserts', 70.00, 'Purple yam flavored ice cream'),
('Chocolate Cake', 'Desserts', 100.00, 'Rich chocolate cake slice'),
('Cheesecake', 'Desserts', 110.00, 'Creamy cheesecake slice');

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for better query performance
CREATE INDEX `idx_orders_date` ON `orders` (`date`);
CREATE INDEX `idx_orders_status` ON `orders` (`status`);
CREATE INDEX `idx_reservations_date_time` ON `reservations` (`date`, `time`);
CREATE INDEX `idx_reservations_status` ON `reservations` (`status`);
CREATE INDEX `idx_menu_items_category` ON `menu_items` (`category`);
CREATE INDEX `idx_payment_transactions_status` ON `payment_transactions` (`transaction_status`);
CREATE INDEX `idx_email_logs_type_status` ON `email_logs` (`type`, `status`);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for order details with user information
CREATE OR REPLACE VIEW `order_details` AS
SELECT
    o.id,
    o.user_id,
    u.name AS customer_name,
    u.email AS customer_email,
    o.items,
    o.total_price,
    o.payment_method,
    o.status,
    o.date
FROM orders o
LEFT JOIN users u ON o.user_id = u.id;

-- View for reservation details with user and table information
CREATE OR REPLACE VIEW `reservation_details` AS
SELECT
    r.id,
    r.user_id,
    u.name AS customer_name,
    u.email AS customer_email,
    u.phone AS customer_phone,
    r.date,
    r.time,
    r.guests,
    r.status,
    r.created_at,
    GROUP_CONCAT(rt.table_id ORDER BY rt.table_id) AS assigned_tables
FROM reservations r
LEFT JOIN users u ON r.user_id = u.id
LEFT JOIN reservation_tables rt ON r.id = rt.reservation_id
GROUP BY r.id;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

DELIMITER //

-- Procedure to get available tables for a specific date and time
CREATE PROCEDURE GetAvailableTables(
    IN reservation_date DATE,
    IN reservation_time TIME,
    IN required_capacity INT
)
BEGIN
    SELECT t.id, t.capacity
    FROM tables t
    WHERE t.capacity >= required_capacity
    AND t.id NOT IN (
        SELECT DISTINCT rt.table_id
        FROM reservation_tables rt
        JOIN reservations r ON rt.reservation_id = r.id
        WHERE r.date = reservation_date
        AND r.time = reservation_time
        AND r.status IN ('pending', 'confirmed')
    )
    ORDER BY t.capacity ASC, t.id ASC;
END //

-- Procedure to calculate daily sales
CREATE PROCEDURE GetDailySales(IN sales_date DATE)
BEGIN
    SELECT
        COUNT(*) AS total_orders,
        SUM(total_price) AS total_sales,
        AVG(total_price) AS average_order_value
    FROM orders
    WHERE DATE(date) = sales_date
    AND status = 'completed';
END //

DELIMITER ;

-- =====================================================
-- TRIGGERS
-- =====================================================

DELIMITER //

-- Trigger to log email notifications
CREATE TRIGGER after_order_insert
AFTER INSERT ON orders
FOR EACH ROW
BEGIN
    INSERT INTO email_logs (user_id, type, status)
    VALUES (NEW.user_id, 'order', 'sent');
END //

-- Trigger to log reservation notifications
CREATE TRIGGER after_reservation_insert
AFTER INSERT ON reservations
FOR EACH ROW
BEGIN
    INSERT INTO email_logs (user_id, type, status)
    VALUES (NEW.user_id, 'reservation', 'sent');
END //

DELIMITER ;

-- =====================================================
-- END OF SQL FILE
-- =====================================================

-- Note: This SQL file creates the complete database structure
-- for Raymart's Diner Restaurant Management System.
--
-- To use this file:
-- 1. Import it into your MySQL database
-- 2. Update the admin password hash if needed
-- 3. Add your actual menu items and images
-- 4. Configure your application's config.php to match these settings
